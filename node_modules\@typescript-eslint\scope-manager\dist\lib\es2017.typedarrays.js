"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2017_typedarrays = void 0;
const base_config_1 = require("./base-config");
exports.es2017_typedarrays = {
    Float32ArrayConstructor: base_config_1.TYPE,
    Float64ArrayConstructor: base_config_1.TYPE,
    Int16ArrayConstructor: base_config_1.TYPE,
    Int32ArrayConstructor: base_config_1.TYPE,
    Int8ArrayConstructor: base_config_1.TYPE,
    Uint16ArrayConstructor: base_config_1.TYPE,
    Uint32ArrayConstructor: base_config_1.TYPE,
    Uint8ArrayConstructor: base_config_1.TYPE,
    Uint8ClampedArrayConstructor: base_config_1.TYPE,
};
//# sourceMappingURL=es2017.typedarrays.js.map