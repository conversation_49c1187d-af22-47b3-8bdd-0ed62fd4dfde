"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.dom_asynciterable = void 0;
const base_config_1 = require("./base-config");
exports.dom_asynciterable = {
    FileSystemDirectoryHandle: base_config_1.TYPE,
    FileSystemDirectoryHandleAsyncIterator: base_config_1.TYPE,
    ReadableStream: base_config_1.TYPE,
    ReadableStreamAsyncIterator: base_config_1.TYPE,
};
//# sourceMappingURL=dom.asynciterable.js.map