{"version": 3, "file": "no-unsafe-call.js", "sourceRoot": "", "sources": ["../../src/rules/no-unsafe-call.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA,sDAAwC;AAExC,kCAOiB;AAQjB,kBAAe,IAAA,iBAAU,EAAiB;IACxC,IAAI,EAAE,gBAAgB;IACtB,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,0CAA0C;YACvD,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,UAAU,EAAE,2CAA2C;YACvD,cAAc,EAAE;gBACd,wEAAwE;gBACxE,2HAA2H;aAC5H,CAAC,IAAI,CAAC,IAAI,CAAC;YACZ,SAAS,EAAE,mDAAmD;YAC9D,iBAAiB,EAAE,iDAAiD;SACrE;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAC9D,MAAM,gBAAgB,GAAG,OAAO,CAAC,6BAA6B,CAC5D,eAAe,EACf,gBAAgB,CACjB,CAAC;QAEF,SAAS,SAAS,CAChB,IAAmB,EACnB,aAA4B,EAC5B,SAAqB;YAErB,MAAM,IAAI,GAAG,IAAA,mCAA4B,EAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAE1D,IAAI,IAAA,oBAAa,EAAC,IAAI,CAAC,EAAE,CAAC;gBACxB,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACtB,gDAAgD;oBAChD,MAAM,cAAc,GAAG,IAAA,wBAAiB,EAAC,IAAI,CAAC,CAAC;oBAC/C,IACE,cAAc;wBACd,IAAA,oBAAa,EACX,IAAA,mCAA4B,EAAC,QAAQ,EAAE,cAAc,CAAC,CACvD,EACD,CAAC;wBACD,SAAS,GAAG,gBAAgB,CAAC;oBAC/B,CAAC;gBACH,CAAC;gBAED,MAAM,WAAW,GAAG,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;gBAEvD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,aAAa;oBACnB,SAAS;oBACT,IAAI,EAAE;wBACJ,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO;qBAC7C;iBACF,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,IAAI,IAAA,0BAAmB,EAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,EAAE,CAAC;gBAC5D,sFAAsF;gBACtF,EAAE;gBACF,0DAA0D;gBAC1D,EAAE;gBACF,wBAAwB;gBACxB,wEAAwE;gBACxE,mDAAmD;gBACnD,EAAE;gBACF,2CAA2C;gBAC3C,0CAA0C;gBAC1C,mDAAmD;gBAEnD,MAAM,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBAC1D,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACnC,OAAO;gBACT,CAAC;gBAED,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAChD,IAAI,SAAS,KAAK,WAAW,EAAE,CAAC;oBAC9B,IACE,cAAc,CAAC,IAAI,CACjB,SAAS,CAAC,EAAE,CACV,CAAC,OAAO,CAAC,mBAAmB,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC,CAC1D,EACD,CAAC;wBACD,OAAO;oBACT,CAAC;gBACH,CAAC;qBAAM,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACrC,OAAO;gBACT,CAAC;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,aAAa;oBACnB,SAAS;oBACT,IAAI,EAAE;wBACJ,IAAI,EAAE,YAAY;qBACnB;iBACF,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;QACH,CAAC;QAED,OAAO;YACL,2BAA2B,CACzB,IAAuC;gBAEvC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;YACtC,CAAC;YACD,aAAa,CAAC,IAAI;gBAChB,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;YAC5C,CAAC;YACD,kCAAkC,CAAC,IAAmB;gBACpD,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,mBAAmB,CAAC,CAAC;YAC7C,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}