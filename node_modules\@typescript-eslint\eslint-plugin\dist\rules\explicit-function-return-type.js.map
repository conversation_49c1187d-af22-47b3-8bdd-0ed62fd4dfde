{"version": 3, "file": "explicit-function-return-type.js", "sourceRoot": "", "sources": ["../../src/rules/explicit-function-return-type.ts"], "names": [], "mappings": ";;AAEA,oDAA0D;AAI1D,kCAAiD;AACjD,6EAIyC;AAqBzC,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,+BAA+B;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,8DAA8D;SACjE;QACD,QAAQ,EAAE;YACR,iBAAiB,EAAE,kCAAkC;SACtD;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,oDAAoD,EAAE;wBACpD,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,sEAAsE;qBACzE;oBACD,yCAAyC,EAAE;wBACzC,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,6EAA6E;qBAChF;oBACD,YAAY,EAAE;wBACZ,IAAI,EAAE,OAAO;wBACb,WAAW,EACT,gGAAgG;wBAClG,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;yBACf;qBACF;oBACD,gBAAgB,EAAE;wBAChB,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,yFAAyF;qBAC5F;oBACD,mCAAmC,EAAE;wBACnC,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,sEAAsE;qBACzE;oBACD,yBAAyB,EAAE;wBACzB,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,gFAAgF;qBACnF;oBACD,UAAU,EAAE;wBACV,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,qEAAqE;qBACxE;oBACD,6BAA6B,EAAE;wBAC7B,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,6EAA6E;qBAChF;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,oDAAoD,EAAE,KAAK;YAC3D,yCAAyC,EAAE,IAAI;YAC/C,YAAY,EAAE,EAAE;YAChB,gBAAgB,EAAE,KAAK;YACvB,mCAAmC,EAAE,KAAK;YAC1C,yBAAyB,EAAE,IAAI;YAC/B,UAAU,EAAE,KAAK;YACjB,6BAA6B,EAAE,IAAI;SACpC;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,iBAAiB,GAAiC,EAAE,CAAC;QAE3D,SAAS,aAAa,CAAC,IAAkB;YACvC,iBAAiB,CAAC,IAAI,CAAC;gBACrB,IAAI;gBACJ,OAAO,EAAE,EAAE;aACZ,CAAC,CAAC;QACL,CAAC;QAED,SAAS,eAAe,CAAC,YAAoB;YAC3C,OAAO,IAAA,iBAAU,EACf,iBAAiB,CAAC,GAAG,EAAE,EACvB,yBAAyB,YAAY,OAAO,CAC7C,CAAC;QACJ,CAAC;QAED,SAAS,iBAAiB,CACxB,IAG+B;YAE/B,IAAI,OAAO,CAAC,mCAAmC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,OAAO,CAAC,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvC,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,MAAM,EAAE,CAAC;gBAClC,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB;gBACpD,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAC/C,CAAC;gBACD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC3B,IAAI,QAAQ,CAAC;gBACb,IAAI,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC;oBAClB,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;gBAC1B,CAAC;qBAAM,CAAC;oBACN,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;wBACpB,KAAK,sBAAc,CAAC,kBAAkB,CAAC,CAAC,CAAC;4BACvC,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;gCACjD,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC;4BAC5B,CAAC;4BACD,MAAM;wBACR,CAAC;wBACD,KAAK,sBAAc,CAAC,gBAAgB,CAAC;wBACrC,KAAK,sBAAc,CAAC,kBAAkB,CAAC;wBACvC,KAAK,sBAAc,CAAC,QAAQ,CAAC,CAAC,CAAC;4BAC7B,IACE,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gCAC7C,CAAC,MAAM,CAAC,QAAQ,EAChB,CAAC;gCACD,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;4BAC7B,CAAC;4BACD,MAAM;wBACR,CAAC;oBACH,CAAC;gBACH,CAAC;gBACD,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC5D,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;YACD,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;gBAChD,IAAI,CAAC,EAAE;gBACP,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAC7C,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,MAAM,CACb,IAG+B;YAE/B,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,CAAC;QAC5D,CAAC;QAED,SAAS,sBAAsB,CAC7B,IAAoE;YAEpE,MAAM,IAAI,GAAG,eAAe,CAAC,qBAAqB,CAAC,CAAC;YAEpD,IACE,OAAO,CAAC,oDAAoD;gBAC5D,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB;gBACpD,IAAI,CAAC,UAAU;gBACf,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBACjD,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,MAAM,EAC7B,CAAC;gBACD,OAAO;YACT,CAAC;YAED,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC5B,OAAO;YACT,CAAC;YAED,IACE,OAAO,CAAC,6BAA6B;gBACrC,CAAC,IAAA,6DAAmC,EAAC,IAAI,EAAE,OAAO,CAAC;oBACjD,IAAA,+CAAqB,EAAC,IAAI,CAAC,CAAC,EAC9B,CAAC;gBACD,OAAO;YACT,CAAC;YAED,IAAA,iDAAuB,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE,CAC/D,OAAO,CAAC,MAAM,CAAC;gBACb,GAAG;gBACH,IAAI;gBACJ,SAAS,EAAE,mBAAmB;aAC/B,CAAC,CACH,CAAC;QACJ,CAAC;QAED,OAAO;YACL,kEAAkE,EAChE,aAAa;YACf,8BAA8B,EAAE,sBAAsB;YACtD,0BAA0B,CAAC,IAAI;gBAC7B,MAAM,IAAI,GAAG,eAAe,CAAC,sBAAsB,CAAC,CAAC;gBACrD,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC5B,OAAO;gBACT,CAAC;gBACD,IAAI,OAAO,CAAC,6BAA6B,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;oBAC7D,OAAO;gBACT,CAAC;gBAED,IAAA,iDAAuB,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE,CAC/D,OAAO,CAAC,MAAM,CAAC;oBACb,GAAG;oBACH,IAAI;oBACJ,SAAS,EAAE,mBAAmB;iBAC/B,CAAC,CACH,CAAC;YACJ,CAAC;YACD,yBAAyB,EAAE,sBAAsB;YACjD,eAAe,CAAC,IAAI;gBAClB,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}