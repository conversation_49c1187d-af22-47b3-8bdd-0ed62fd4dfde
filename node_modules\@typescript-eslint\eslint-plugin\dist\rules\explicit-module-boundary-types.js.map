{"version": 3, "file": "explicit-module-boundary-types.js", "sourceRoot": "", "sources": ["../../src/rules/explicit-module-boundary-types.ts"], "names": [], "mappings": ";;AAEA,oEAAkE;AAClE,oDAA0D;AAQ1D,kCAA8E;AAC9E,6EAMyC;AAkBzC,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,gCAAgC;IACtC,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,qGAAqG;SACxG;QACD,QAAQ,EAAE;YACR,WAAW,EAAE,0DAA0D;YACvE,kBAAkB,EAChB,wDAAwD;YAC1D,cAAc,EAAE,sCAAsC;YACtD,qBAAqB,EAAE,oCAAoC;YAC3D,iBAAiB,EAAE,kCAAkC;SACtD;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,kCAAkC,EAAE;wBAClC,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,iEAAiE;qBACpE;oBACD,yCAAyC,EAAE;wBACzC,IAAI,EAAE,SAAS;wBACf,WAAW,EAAE;4BACX,kHAAkH;4BAClH,qDAAqD;yBACtD,CAAC,IAAI,CAAC,IAAI,CAAC;qBACb;oBACD,YAAY,EAAE;wBACZ,IAAI,EAAE,OAAO;wBACb,WAAW,EACT,gGAAgG;wBAClG,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;yBACf;qBACF;oBACD,yBAAyB,EAAE;wBACzB,IAAI,EAAE,SAAS;wBACf,WAAW,EAAE;4BACX,2GAA2G;4BAC3G,qDAAqD;yBACtD,CAAC,IAAI,CAAC,IAAI,CAAC;qBACb;oBACD,6BAA6B,EAAE;wBAC7B,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,8EAA8E;qBACjF;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,kCAAkC,EAAE,KAAK;YACzC,yCAAyC,EAAE,IAAI;YAC/C,YAAY,EAAE,EAAE;YAChB,yBAAyB,EAAE,IAAI;YAC/B,6BAA6B,EAAE,IAAI;SACpC;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,oDAAoD;QACpD,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAgB,CAAC;QAEjD,MAAM,aAAa,GAAmB,EAAE,CAAC;QACzC,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAG/B,CAAC;QAEJ,qEAAqE;QACrE,6CAA6C;QAC7C,MAAM,cAAc,GAAG,IAAI,GAAG,EAAiB,CAAC;QAEhD,SAAS,oBAAoB,CAC3B,IAAkB;YAElB,OAAO,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAC5C,CAAC;QAED,SAAS,aAAa,CAAC,IAAkB;YACvC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzB,kBAAkB,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACnC,CAAC;QAED,SAAS,YAAY;YACnB,aAAa,CAAC,GAAG,EAAE,CAAC;QACtB,CAAC;QAED;;;;;;;;;UASE;QAEF,OAAO;YACL,kEAAkE,EAChE,aAAa;YACf,8BAA8B,EAAE,YAAY;YAC5C,+BAA+B,CAAC,IAAI;gBAClC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC9B,CAAC;YACD,2CAA2C,CACzC,IAAkD;gBAElD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBACrB,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC9B,CAAC;qBAAM,CAAC;oBACN,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;wBACxC,eAAe,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;oBACnC,CAAC;gBACH,CAAC;YACH,CAAC;YACD,0BAA0B,EAAE,YAAY;YACxC,yBAAyB,EAAE,YAAY;YACvC,cAAc;gBACZ,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,kBAAkB,EAAE,CAAC;oBACjD,IAAI,6BAA6B,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;wBACrD,SAAS,CAAC,IAAI,CAAC,CAAC;oBAClB,CAAC;gBACH,CAAC;YACH,CAAC;YACD,eAAe,CAAC,IAAI;gBAClB,MAAM,OAAO,GAAG,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACxD,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,CAAC;YACD,yBAAyB,CAAC,IAAI;gBAC5B,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC7B,CAAC;SACF,CAAC;QAEF,SAAS,eAAe,CACtB,IAA2D;YAE3D,SAAS,cAAc,CAAC,KAAyB;gBAC/C,SAAS,MAAM,CACb,cAA0B,EAC1B,gBAA4B;oBAE5B,IAAI,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;wBAC7C,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,KAAK;4BACX,SAAS,EAAE,cAAc;4BACzB,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE;yBAC3B,CAAC,CAAC;oBACL,CAAC;yBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY,EAAE,CAAC;wBACtD,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,KAAK;4BACX,SAAS,EAAE,gBAAgB;4BAC3B,IAAI,EAAE,EAAE,IAAI,EAAE,eAAe,EAAE;yBAChC,CAAC,CAAC;oBACL,CAAC;yBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,EAAE,CAAC;wBACvD,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,KAAK;4BACX,SAAS,EAAE,gBAAgB;4BAC3B,IAAI,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE;yBACjC,CAAC,CAAC;oBACL,CAAC;yBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,EAAE,CAAC;wBACrD,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;4BACtD,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE,KAAK;gCACX,SAAS,EAAE,cAAc;gCACzB,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE;6BACpC,CAAC,CAAC;wBACL,CAAC;6BAAM,CAAC;4BACN,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE,KAAK;gCACX,SAAS,EAAE,gBAAgB;gCAC3B,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;6BACvB,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;oBACnB,KAAK,sBAAc,CAAC,YAAY,CAAC;oBACjC,KAAK,sBAAc,CAAC,UAAU,CAAC;oBAC/B,KAAK,sBAAc,CAAC,aAAa,CAAC;oBAClC,KAAK,sBAAc,CAAC,WAAW;wBAC7B,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;4BAC1B,MAAM,CAAC,gBAAgB,EAAE,uBAAuB,CAAC,CAAC;wBACpD,CAAC;6BAAM,IACL,OAAO,CAAC,kCAAkC,KAAK,IAAI;4BACnD,KAAK,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI;gCACtC,sBAAc,CAAC,YAAY,EAC7B,CAAC;4BACD,MAAM,CAAC,aAAa,EAAE,oBAAoB,CAAC,CAAC;wBAC9C,CAAC;wBACD,OAAO;oBAET,KAAK,sBAAc,CAAC,mBAAmB;wBACrC,OAAO,cAAc,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;oBAEzC,KAAK,sBAAc,CAAC,iBAAiB,EAAE,8CAA8C;wBACnF,OAAO;gBACX,CAAC;YACH,CAAC;YAED,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC9B,cAAc,CAAC,GAAG,CAAC,CAAC;YACtB,CAAC;QACH,CAAC;QAED;;WAEG;QACH,SAAS,aAAa,CAAC,IAA+B;YACpD,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACxE,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;gBAC/C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EAChD,CAAC;gBACD,OAAO,CACL,IAAI,CAAC,EAAE,EAAE,IAAI,KAAK,sBAAc,CAAC,UAAU;oBAC3C,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAC5C,CAAC;YACJ,CAAC;iBAAM,IACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC7C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,0BAA0B;gBACvD,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC;gBACtD,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAC/C,CAAC;gBACD,OAAO,IAAA,kCAA2B,EAChC,IAAI,EACJ,OAAO,EACP,GAAG,OAAO,CAAC,YAAY,CACxB,CAAC;YACJ,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,6BAA6B,CAAC,EACrC,IAAI,GACuB;YAC3B,IAAI,OAAO,GAA8B,IAAI,CAAC,MAAM,CAAC;YACrD,OAAO,OAAO,EAAE,CAAC;gBACf,IAAI,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE,CAAC;oBACpD,kFAAkF;oBAClF,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC;oBAChC,SAAS;gBACX,CAAC;gBAED,IAAI,CAAC,IAAA,iBAAU,EAAC,OAAO,CAAC,EAAE,CAAC;oBACzB,OAAO,KAAK,CAAC;gBACf,CAAC;gBACD,MAAM,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;gBAC9C,IACE,CAAC,IAAA,iEAAuC,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,EACpE,CAAC;oBACD,OAAO,KAAK,CAAC;gBACf,CAAC;gBAED,IAAI,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;oBAClC,OAAO,IAAI,CAAC;gBACd,CAAC;gBAED,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;YAC3B,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,eAAe,CAAC,IAAyB;YAChD,MAAM,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,wBAAwB,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACvC,OAAO;YACT,CAAC;YAED,+BAA+B;YAC/B,KAAK,MAAM,UAAU,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACvC,yCAAyC;gBACzC,IACE;oBACE,8BAAc,CAAC,WAAW;oBAC1B,8BAAc,CAAC,sBAAsB;oBACrC,8BAAc,CAAC,aAAa;oBAC5B,8BAAc,CAAC,SAAS;iBACzB,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAC3B,CAAC;oBACD,SAAS;gBACX,CAAC;gBAED,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAC7B,CAAC;YAED,mDAAmD;YACnD,KAAK,MAAM,SAAS,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;gBAC5C;gBACE,6FAA6F;gBAC7F,CAAC,SAAS,CAAC,IAAI;oBACf,SAAS,CAAC,SAAS,EACnB,CAAC;oBACD,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBACjC,CAAC;YACH,CAAC;QACH,CAAC;QAED,SAAS,SAAS,CAAC,IAA0B;YAC3C,IAAI,IAAI,IAAI,IAAI,IAAI,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC7C,OAAO;YACT,CAAC;YACD,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAEzB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;gBAClB,KAAK,sBAAc,CAAC,uBAAuB,CAAC;gBAC5C,KAAK,sBAAc,CAAC,kBAAkB,CAAC,CAAC,CAAC;oBACvC,MAAM,OAAO,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC;oBAC3C,OAAO,uBAAuB,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;gBACpD,CAAC;gBAED,KAAK,sBAAc,CAAC,eAAe;oBACjC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;wBACpC,SAAS,CAAC,OAAO,CAAC,CAAC;oBACrB,CAAC;oBACD,OAAO;gBAET,KAAK,sBAAc,CAAC,kBAAkB,CAAC;gBACvC,KAAK,sBAAc,CAAC,gBAAgB,CAAC;gBACrC,KAAK,sBAAc,CAAC,0BAA0B;oBAC5C,IACE,IAAI,CAAC,aAAa,KAAK,SAAS;wBAChC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAClD,CAAC;wBACD,OAAO;oBACT,CAAC;oBACD,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAE/B,KAAK,sBAAc,CAAC,gBAAgB,CAAC;gBACrC,KAAK,sBAAc,CAAC,eAAe;oBACjC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;wBACrC,SAAS,CAAC,OAAO,CAAC,CAAC;oBACrB,CAAC;oBACD,OAAO;gBAET,KAAK,sBAAc,CAAC,mBAAmB,CAAC,CAAC,CAAC;oBACxC,MAAM,OAAO,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC;oBAC3C,OAAO,aAAa,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;gBAC1C,CAAC;gBAED,KAAK,sBAAc,CAAC,UAAU;oBAC5B,OAAO,eAAe,CAAC,IAAI,CAAC,CAAC;gBAE/B,KAAK,sBAAc,CAAC,gBAAgB;oBAClC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;wBACvC,SAAS,CAAC,QAAQ,CAAC,CAAC;oBACtB,CAAC;oBACD,OAAO;gBAET,KAAK,sBAAc,CAAC,QAAQ;oBAC1B,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAE/B,KAAK,sBAAc,CAAC,6BAA6B;oBAC/C,OAAO,gCAAgC,CAAC,IAAI,CAAC,CAAC;gBAEhD,KAAK,sBAAc,CAAC,mBAAmB;oBACrC,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;wBAC5C,SAAS,CAAC,WAAW,CAAC,CAAC;oBACzB,CAAC;oBACD,OAAO;gBAET,KAAK,sBAAc,CAAC,kBAAkB;oBACpC,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;QAED,SAAS,gCAAgC,CACvC,IAA4C;YAE5C,MAAM,aAAa,GACjB,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBACpD,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,aAAa,CAAC;YACrC,MAAM,aAAa,GACjB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,0BAA0B;gBAC7D,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,CAAC;gBACvD,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC;YAC7B,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACzD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,mBAAmB;iBAC/B,CAAC,CAAC;YACL,CAAC;YAED,eAAe,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC;QAED,SAAS,uBAAuB,CAAC,EAC/B,IAAI,EACJ,OAAO,GAC0B;YACjC,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC/B,OAAO;YACT,CAAC;YACD,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAE3B,IACE,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC1B,IAAA,mDAAyB,EAAC,IAAI,EAAE,OAAO,CAAC;gBACxC,IAAA,+CAAqB,EAAC,IAAI,CAAC,EAC3B,CAAC;gBACD,OAAO;YACT,CAAC;YAED,IAAA,2DAAiC,EAC/B,EAAE,IAAI,EAAE,OAAO,EAAE,EACjB,OAAO,EACP,OAAO,CAAC,UAAU,EAClB,GAAG,CAAC,EAAE;gBACJ,OAAO,CAAC,MAAM,CAAC;oBACb,GAAG;oBACH,IAAI;oBACJ,SAAS,EAAE,mBAAmB;iBAC/B,CAAC,CAAC;YACL,CAAC,CACF,CAAC;YAEF,eAAe,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC;QAED,SAAS,aAAa,CAAC,EACrB,IAAI,EACJ,OAAO,GACoC;YAC3C,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC/B,OAAO;YACT,CAAC;YACD,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAE3B,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,IAAA,+CAAqB,EAAC,IAAI,CAAC,EAAE,CAAC;gBACvD,OAAO;YACT,CAAC;YAED,IAAA,iDAAuB,EACrB,EAAE,IAAI,EAAE,OAAO,EAAE,EACjB,OAAO,EACP,OAAO,CAAC,UAAU,EAClB,GAAG,CAAC,EAAE;gBACJ,OAAO,CAAC,MAAM,CAAC;oBACb,GAAG;oBACH,IAAI;oBACJ,SAAS,EAAE,mBAAmB;iBAC/B,CAAC,CAAC;YACL,CAAC,CACF,CAAC;YAEF,eAAe,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC;IACH,CAAC;CACF,CAAC,CAAC"}