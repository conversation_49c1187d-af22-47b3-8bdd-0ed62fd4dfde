{"version": 3, "file": "no-deprecated.js", "sourceRoot": "", "sources": ["../../src/rules/no-deprecated.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA,oDAA0D;AAC1D,sDAAwC;AACxC,+CAAiC;AAEjC,kCAAoE;AAIpE,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,eAAe;IACrB,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,6CAA6C;YAC1D,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,UAAU,EAAE,6BAA6B;YACzC,oBAAoB,EAAE,wCAAwC;SAC/D;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC,aAAa,CAAC;QACnD,IAAI,gBAAgB,KAAK,MAAM,IAAI,gBAAgB,KAAK,WAAW,EAAE,CAAC;YACpE,MAAM,IAAI,KAAK,CACb,0CAA0C,gBAAgB,IAAI,CAC/D,CAAC;QACJ,CAAC;QAED,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,gEAAgE;QAChE,EAAE;QACF,oCAAoC;QACpC,EAAE;QACF,0EAA0E;QAC1E,0EAA0E;QAC1E,yEAAyE;QACzE,sCAAsC;QACtC,SAAS,kCAAkC,CACzC,MAA6B,EAC7B,gCAAyC;YAEzC,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;gBACtE,OAAO,gCAAgC;oBACrC,CAAC,CAAC,mBAAmB,CAAC,MAAM,CAAC;oBAC7B,CAAC,CAAC,SAAS,CAAC;YAChB,CAAC;YACD,MAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YACtD,OAAO,OAAO,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC7D,MAAM,MAAM,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBAC3C,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;oBACzB,OAAO,MAAM,CAAC;gBAChB,CAAC;gBACD,MAAM,sBAAsB,GAC1B,MAAM,CAAC,eAAe,EAAE,IAAI,OAAO,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;gBACxE,IAAI,CAAC,sBAAsB,EAAE,CAAC;oBAC5B,MAAM;gBACR,CAAC;gBACD,MAAM,GAAG,sBAAsB,CAAC;gBAChC,IAAI,gCAAgC,IAAI,MAAM,KAAK,YAAY,EAAE,CAAC;oBAChE,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBACrC,CAAC;YACH,CAAC;YACD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,SAAS,aAAa,CAAC,IAAoB;YACzC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;YAExB,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;gBACpB,KAAK,sBAAc,CAAC,YAAY;oBAC9B,OAAO,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAA2B,CAAC,CAAC;gBAE/D,KAAK,sBAAc,CAAC,eAAe,CAAC;gBACpC,KAAK,sBAAc,CAAC,gBAAgB,CAAC;gBACrC,KAAK,sBAAc,CAAC,kBAAkB,CAAC;gBACvC,KAAK,sBAAc,CAAC,YAAY;oBAC9B,OAAO,MAAM,CAAC,EAAE,KAAK,IAAI,CAAC;gBAE5B,KAAK,sBAAc,CAAC,gBAAgB,CAAC;gBACrC,KAAK,sBAAc,CAAC,kBAAkB;oBACpC,OAAO,MAAM,CAAC,GAAG,KAAK,IAAI,CAAC;gBAE7B,KAAK,sBAAc,CAAC,QAAQ;oBAC1B,sEAAsE;oBACtE,4DAA4D;oBAC5D,OAAO,CACL,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;wBAC3C,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,CACvD,CAAC;gBAEJ,KAAK,sBAAc,CAAC,iBAAiB;oBACnC,kFAAkF;oBAClF,2DAA2D;oBAC3D,OAAO,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC;gBAE9B,KAAK,sBAAc,CAAC,uBAAuB,CAAC;gBAC5C,KAAK,sBAAc,CAAC,mBAAmB,CAAC;gBACxC,KAAK,sBAAc,CAAC,kBAAkB,CAAC;gBACvC,KAAK,sBAAc,CAAC,iBAAiB,CAAC;gBACtC,KAAK,sBAAc,CAAC,6BAA6B,CAAC;gBAClD,KAAK,sBAAc,CAAC,iBAAiB,CAAC;gBACtC,KAAK,sBAAc,CAAC,sBAAsB,CAAC;gBAC3C,KAAK,sBAAc,CAAC,iBAAiB,CAAC;gBACtC,KAAK,sBAAc,CAAC,mBAAmB,CAAC;gBACxC,KAAK,sBAAc,CAAC,mBAAmB,CAAC;gBACxC,KAAK,sBAAc,CAAC,mBAAmB,CAAC;gBACxC,KAAK,sBAAc,CAAC,sBAAsB,CAAC;gBAC3C,KAAK,sBAAc,CAAC,eAAe;oBACjC,OAAO,IAAI,CAAC;gBAEd;oBACE,OAAO,KAAK,CAAC;YACjB,CAAC;QACH,CAAC;QAED,SAAS,sBAAsB,CAAC,IAAmB;YACjD,IAAI,OAAO,GAAG,IAAI,CAAC;YAEnB,OAAO,IAAI,EAAE,CAAC;gBACZ,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;oBACrB,KAAK,sBAAc,CAAC,oBAAoB,CAAC;oBACzC,KAAK,sBAAc,CAAC,wBAAwB,CAAC;oBAC7C,KAAK,sBAAc,CAAC,sBAAsB,CAAC;oBAC3C,KAAK,sBAAc,CAAC,iBAAiB,CAAC;oBACtC,KAAK,sBAAc,CAAC,gBAAgB;wBAClC,OAAO,IAAI,CAAC;oBAEd,KAAK,sBAAc,CAAC,uBAAuB,CAAC;oBAC5C,KAAK,sBAAc,CAAC,cAAc,CAAC;oBACnC,KAAK,sBAAc,CAAC,SAAS,CAAC;oBAC9B,KAAK,sBAAc,CAAC,sBAAsB,CAAC;oBAC3C,KAAK,sBAAc,CAAC,mBAAmB,CAAC;oBACxC,KAAK,sBAAc,CAAC,kBAAkB,CAAC;oBACvC,KAAK,sBAAc,CAAC,OAAO,CAAC;oBAC5B,KAAK,sBAAc,CAAC,WAAW,CAAC;oBAChC,KAAK,sBAAc,CAAC,kBAAkB;wBACpC,OAAO,KAAK,CAAC;oBAEf;wBACE,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;gBAC7B,CAAC;YACH,CAAC;QACH,CAAC;QAED,SAAS,mBAAmB,CAC1B,MAA4C;YAE5C,IAAI,SAAwC,CAAC;YAC7C,IAAI,CAAC;gBACH,SAAS,GAAG,MAAM,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;YAC5C,CAAC;YAAC,MAAM,CAAC;gBACP,sEAAsE;gBACtE,OAAO;YACT,CAAC;YACD,MAAM,GAAG,GAAG,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,YAAY,CAAC,CAAC;YAE9D,IAAI,CAAC,GAAG,EAAE,CAAC;gBACT,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC;YAE9B,OAAO,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACnE,CAAC;QAQD,SAAS,oBAAoB,CAAC,IAAmB;YAC/C,QAAQ,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC;gBAC1B,KAAK,sBAAc,CAAC,aAAa,CAAC;gBAClC,KAAK,sBAAc,CAAC,cAAc;oBAChC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC;gBAErC,KAAK,sBAAc,CAAC,wBAAwB;oBAC1C,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,IAAI,CAAC;gBAElC,KAAK,sBAAc,CAAC,iBAAiB;oBACnC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC;gBAEnC;oBACE,OAAO,KAAK,CAAC;YACjB,CAAC;QACH,CAAC;QAED,SAAS,eAAe,CAAC,IAAmB;YAC1C,IAAI,MAAM,GAAG,IAAI,CAAC;YAElB,OACE,MAAM,CAAC,MAAM,EAAE,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBACvD,MAAM,CAAC,MAAM,CAAC,QAAQ,KAAK,MAAM,EACjC,CAAC;gBACD,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;YACzB,CAAC;YAED,OAAO,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;QAC3D,CAAC;QAED,SAAS,sBAAsB,CAAC,IAAkB;YAChD,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAE/D,oEAAoE;YACpE,MAAM,SAAS,GAAG,IAAA,iBAAU,EAC1B,OAAO,CAAC,oBAAoB,CAAC,MAA+B,CAAC,EAC7D,2CAA2C,CAC5C,CAAC;YAEF,MAAM,MAAM,GAAG,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,aAAa,GACjB,MAAM,KAAK,SAAS;gBACpB,OAAO,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC;gBACnD,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC;gBAClC,CAAC,CAAC,MAAM,CAAC;YACb,MAAM,qBAAqB,GAAG,aAAa,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACpE,8DAA8D;YAC9D,6CAA6C;YAC7C,EAAE;YACF,oBAAoB;YACpB,uBAAuB;YACvB,0BAA0B;YAC1B,0BAA0B;YAC1B,IAAI;YACJ,IACE,qBAAqB,KAAK,EAAE,CAAC,UAAU,CAAC,iBAAiB;gBACzD,qBAAqB,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB;gBAC3D,qBAAqB,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe,EACvD,CAAC;gBACD,OAAO,CACL,kCAAkC,CAAC,MAAM,EAAE,IAAI,CAAC;oBAChD,mBAAmB,CAAC,SAAS,CAAC;oBAC9B,mBAAmB,CAAC,aAAa,CAAC,CACnC,CAAC;YACJ,CAAC;YACD,OAAO,CACL,kCAAkC,CAChC,MAAM;YACN,4DAA4D;YAC5D,+DAA+D;YAC/D,yDAAyD;YACzD,EAAE;YACF,8BAA8B;YAC9B,EAAE;YACF,uBAAuB;YACvB,iCAAiC;YACjC,kCAAkC;YAClC,sCAAsC;YACtC,EAAE;YACF,mCAAmC;YACnC,EAAE;YACF,oEAAoE;YACpE,+DAA+D;YAC/D,sCAAsC;YACtC,kEAAkE;YAClE,sEAAsE;YACtE,oEAAoE;YACpE,oCAAoC;YACpC,EAAE;YACF,2EAA2E;YAC3E,6EAA6E;YAC7E,KAAK,CACN,IAAI,mBAAmB,CAAC,SAAS,CAAC,CACpC,CAAC;QACJ,CAAC;QAED,SAAS,oBAAoB,CAAC,IAAoB;YAChD,MAAM,YAAY,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;YAC3C,IAAI,YAAY,EAAE,CAAC;gBACjB,OAAO,sBAAsB,CAAC,YAAY,CAAC,CAAC;YAC9C,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ,EAAE,CAAC;gBACjD,OAAO,mBAAmB,CACxB,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CACtE,CAAC;YACJ,CAAC;YACD,OAAO,kCAAkC,CACvC,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAClC,IAAI,CACL,CAAC;QACJ,CAAC;QAED,SAAS,eAAe,CAAC,IAAoB;YAC3C,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE,CAAC;gBACxD,OAAO;YACT,CAAC;YAED,MAAM,MAAM,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC;YAC1C,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gBACzB,OAAO;YACT,CAAC;YAED,OAAO,CAAC,MAAM,CAAC;gBACb,GAAG,CAAC,MAAM;oBACR,CAAC,CAAC;wBACE,SAAS,EAAE,sBAAsB;wBACjC,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE;qBAClC;oBACH,CAAC,CAAC;wBACE,SAAS,EAAE,YAAY;wBACvB,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE;qBAC1B,CAAC;gBACN,IAAI;aACL,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,UAAU,EAAE,eAAe;YAC3B,aAAa,CAAC,IAAI;gBAChB,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE,CAAC;oBAC1D,eAAe,CAAC,IAAI,CAAC,CAAC;gBACxB,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}