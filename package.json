{"name": "professional-task-management-saas", "private": false, "version": "1.0.0", "type": "module", "description": "A modern, privacy-first task management SaaS with advanced analytics, voice integration, and professional-grade features", "keywords": ["task-management", "productivity", "calendar", "analytics", "voice-integration", "saas", "react", "typescript", "privacy-first", "mobile-app"], "author": {"name": "Your Name", "email": "<EMAIL>", "url": "https://yourwebsite.com"}, "license": "MIT", "homepage": "https://github.com/adamp181/scheduled-task-view#readme", "repository": {"type": "git", "url": "git+https://github.com/adamp181/scheduled-task-view.git"}, "bugs": {"url": "https://github.com/adamp181/scheduled-task-view/issues"}, "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "clean": "rm -rf dist node_modules/.vite", "analyze": "npx vite-bundle-analyzer", "prepare": "husky install"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@tanstack/react-query": "^5.59.16", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "framer-motion": "^11.11.17", "i18next": "^23.16.4", "lucide-react": "^0.454.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.1", "react-i18next": "^15.1.1", "react-router-dom": "^6.28.0", "sonner": "^1.7.3", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "vaul": "^1.0.0", "zod": "^3.23.8", "zustand": "^5.0.1"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/node": "^22.8.6", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react-swc": "^3.7.1", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "husky": "^9.1.6", "postcss": "^8.4.47", "prettier": "^3.3.3", "tailwindcss": "^3.4.14", "typescript": "~5.6.2", "typescript-eslint": "^8.11.0", "vite": "^5.4.10", "vite-plugin-pwa": "^0.20.5", "vitest": "^2.1.3", "workbox-window": "^7.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}