{"version": 3, "file": "no-unsafe-declaration-merging.js", "sourceRoot": "", "sources": ["../../src/rules/no-unsafe-declaration-merging.ts"], "names": [], "mappings": ";;AAGA,oDAA0D;AAE1D,kCAAqC;AAErC,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,+BAA+B;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,qCAAqC;YAClD,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,KAAK;SAC5B;QACD,QAAQ,EAAE;YACR,aAAa,EACX,4DAA4D;SAC/D;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,SAAS,sBAAsB,CAC7B,KAAY,EACZ,IAAyB,EACzB,UAA0B;YAE1B,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO;YACT,CAAC;YAED,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;YAC3B,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBACrB,OAAO;YACT,CAAC;YAED,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,EAAE,CAAC;gBACnD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,eAAe;iBAC3B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,gBAAgB,CAAC,IAAI;gBACnB,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;oBACZ,gFAAgF;oBAChF,qEAAqE;oBACrE,MAAM,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;oBAC7D,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;wBACzB,OAAO;oBACT,CAAC;oBAED,sBAAsB,CACpB,YAAY,EACZ,IAAI,CAAC,EAAE,EACP,sBAAc,CAAC,sBAAsB,CACtC,CAAC;gBACJ,CAAC;YACH,CAAC;YACD,sBAAsB,CAAC,IAAI;gBACzB,sBAAsB,CACpB,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EACjC,IAAI,CAAC,EAAE,EACP,sBAAc,CAAC,gBAAgB,CAChC,CAAC;YACJ,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}