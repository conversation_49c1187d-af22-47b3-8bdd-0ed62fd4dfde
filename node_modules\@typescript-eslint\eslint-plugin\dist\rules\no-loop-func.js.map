{"version": 3, "file": "no-loop-func.js", "sourceRoot": "", "sources": ["../../src/rules/no-loop-func.ts"], "names": [], "mappings": ";;AAEA,oDAA0D;AAO1D,kCAAqC;AACrC,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,cAAc,CAAC,CAAC;AAKnD,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,cAAc;IACpB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,sFAAsF;YACxF,eAAe,EAAE,IAAI;SACtB;QACD,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;QAChC,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAI/B,CAAC;QAEJ;;;;;;;;WAQG;QACH,SAAS,qBAAqB,CAAC,IAAmB;YAChD,KACE,IAAI,WAAW,GAAG,IAAI,EACtB,WAAW,CAAC,MAAM,EAClB,WAAW,GAAG,WAAW,CAAC,MAAM,EAChC,CAAC;gBACD,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;gBAElC,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;oBACpB,KAAK,sBAAc,CAAC,cAAc,CAAC;oBACnC,KAAK,sBAAc,CAAC,gBAAgB;wBAClC,OAAO,MAAM,CAAC;oBAEhB,KAAK,sBAAc,CAAC,YAAY;wBAC9B,iCAAiC;wBACjC,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;4BAChC,OAAO,MAAM,CAAC;wBAChB,CAAC;wBACD,MAAM;oBAER,KAAK,sBAAc,CAAC,cAAc,CAAC;oBACnC,KAAK,sBAAc,CAAC,cAAc;wBAChC,kCAAkC;wBAClC,IAAI,MAAM,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;4BACjC,OAAO,MAAM,CAAC;wBAChB,CAAC;wBACD,MAAM;oBAER,KAAK,sBAAc,CAAC,uBAAuB,CAAC;oBAC5C,KAAK,sBAAc,CAAC,kBAAkB,CAAC;oBACvC,KAAK,sBAAc,CAAC,mBAAmB;wBACrC,2CAA2C;wBAE3C,0DAA0D;wBAC1D,IAAI,kBAAkB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;4BACnC,MAAM;wBACR,CAAC;wBACD,OAAO,IAAI,CAAC;oBAEd;wBACE,MAAM;gBACV,CAAC;YACH,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED;;;;;;WAMG;QACH,SAAS,cAAc,CACrB,IAAmB,EACnB,YAA8C;YAE9C,MAAM,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,IAAI,IAAI,GAAG,IAAI,CAAC;YAChB,IAAI,kBAAkB,GAAyB,IAAI,CAAC;YAEpD,OAAO,kBAAkB,IAAI,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC;gBACnE,IAAI,GAAG,kBAAkB,CAAC;gBAC1B,kBAAkB,GAAG,qBAAqB,CAAC,kBAAkB,CAAC,CAAC;YACjE,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED;;;;;;WAMG;QACH,SAAS,MAAM,CACb,QAAuB,EACvB,SAAmC;YAEnC,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;YACpC,MAAM,UAAU,GAAG,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,WAAW,GAAG,UAAU,EAAE,MAAM,CAAC;YACvC,MAAM,IAAI,GACR,WAAW,EAAE,IAAI,KAAK,sBAAc,CAAC,mBAAmB;gBACtD,CAAC,CAAC,WAAW,CAAC,IAAI;gBAClB,CAAC,CAAC,EAAE,CAAC;YAET,+BAA+B;YAC/B,yEAAyE;YACzE,IAAI,SAAS,CAAC,eAAe,EAAE,CAAC;gBAC9B,OAAO,IAAI,CAAC;YACd,CAAC;YAED,mDAAmD;YACnD,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;gBACrB,OAAO,IAAI,CAAC;YACd,CAAC;YAED;;;eAGG;YACH,IACE,IAAI,KAAK,KAAK;gBACd,WAAW;gBACX,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;gBACxC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EACxC,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAED;;;eAGG;YACH,MAAM,MAAM,GAAG,cAAc,CAC3B,QAAQ,EACR,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CACpC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAEX;;;;;;;;;;;eAWG;YACH,SAAS,eAAe,CAAC,QAAkC;gBACzD,MAAM,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC;gBAE/B,OAAO,CACL,CAAC,QAAQ,CAAC,OAAO,EAAE;oBACnB,CAAC,QAAQ,EAAE,KAAK,CAAC,aAAa,KAAK,QAAQ,CAAC,IAAI,CAAC,aAAa;wBAC5D,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CACxB,CAAC;YACJ,CAAC;YAED,OAAO,QAAQ,EAAE,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,KAAK,CAAC;QAC9D,CAAC;QAED;;;;;;WAMG;QACH,SAAS,aAAa,CACpB,IAG+B;YAE/B,MAAM,QAAQ,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAE7C,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO;YACT,CAAC;YAED,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;YAE7D,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBACpD,MAAM,oBAAoB,GACxB,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,CAAC;gBAElD,4DAA4D;gBAC5D,MAAM,oBAAoB,GACxB,oBAAoB,IAAI,IAAI,CAAC,EAAE;oBAC7B,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC;oBAC3D,CAAC,CAAC,KAAK,CAAC;gBAEZ,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAC1B,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBAC7B,OAAO;gBACT,CAAC;YACH,CAAC;YAED,MAAM,UAAU,GAAG,UAAU;iBAC1B,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;iBAC/C,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAE/B,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1B,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,YAAY;oBACvB,IAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;iBACnD,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,uBAAuB,EAAE,aAAa;YACtC,mBAAmB,EAAE,aAAa;YAClC,kBAAkB,EAAE,aAAa;SAClC,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,MAAM,CACb,IAG+B;IAE/B,OAAO,CACL,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;QAClD,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,IAAI,CAC5B,CAAC;AACJ,CAAC"}