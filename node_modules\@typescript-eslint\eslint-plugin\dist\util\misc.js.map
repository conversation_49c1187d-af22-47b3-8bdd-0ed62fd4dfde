{"version": 3, "file": "misc.js", "sourceRoot": "", "sources": ["../../src/util/misc.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAqUE,8CAAiB;AACjB,wCAAc;AAGd,0CAAe;AACf,sCAAa;AACb,wCAAc;AACd,oCAAY;AACZ,8DAAyB;AACzB,8CAAiB;AACjB,gEAA0B;AAC1B,4CAAgB;AAChB,4DAAwB;AACxB,gEAA0B;AAI1B,kEAA2B;AAC3B,wCAAc;AAjVhB,8DAAgE;AAChE,oDAA0D;AAC1D,+CAAiC;AAEjC,yCAA6D;AAE7D,MAAM,qBAAqB,GAAG;IAC5B,EAAE,CAAC,SAAS,CAAC,GAAG;IAChB,EAAE,CAAC,SAAS,CAAC,IAAI;IACjB,EAAE,CAAC,SAAS,CAAC,IAAI;CACT,CAAC;AACX;;GAEG;AACH,SAAS,gBAAgB,CAAC,QAAgB;IACxC,MAAM,aAAa,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;IAC7C,KAAK,MAAM,aAAa,IAAI,qBAAqB,EAAE,CAAC;QAClD,IAAI,aAAa,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAS,cAAc,CAAC,GAAW;IACjC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC7C,CAAC;AAED,SAAS,iBAAiB,CACxB,KAAU,EACV,MAAwB;IAExB,MAAM,MAAM,GAAG,IAAI,GAAG,EAAY,CAAC;IAEnC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;QACzB,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAEjC,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAKD,SAAS,cAAc,CACrB,CAAkB,EAClB,CAAkB,EAClB,EAA2B;IAE3B,OAAO,CACL,CAAC,KAAK,CAAC;QACP,CAAC,CAAC,KAAK,SAAS;YACd,CAAC,KAAK,SAAS;YACf,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM;YACrB,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACtC,CAAC;AACJ,CAAC;AAED,gDAAgD;AAChD,SAAS,eAAe,CACtB,MAAW,EACX,SAAkC;IAElC,KAAK,MAAM,OAAO,IAAI,MAAM,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACzB,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;GAEG;AACH,SAAS,yBAAyB,CAAC,IAA+B;IAChE,MAAM,QAAQ,GAAsC,IAAI,CAAC,UAAU,CAAC,IAAI,CACtE,CAAC,SAA6B,EAAoC,EAAE,CAClE,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,CAC/C,CAAC;IACF,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB,CAAC;AACxD,CAAC;AAED,IAAK,cAKJ;AALD,WAAK,cAAc;IACjB,yDAAW,CAAA;IACX,uDAAU,CAAA;IACV,uDAAU,CAAA;IACV,+DAAc,CAAA;AAChB,CAAC,EALI,cAAc,8BAAd,cAAc,QAKlB;AAED;;;GAGG;AACH,SAAS,iBAAiB,CACxB,MASgC,EAChC,UAA+B;IAE/B,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;QAClD,OAAO;YACL,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI;YACrB,IAAI,EAAE,cAAc,CAAC,MAAM;SAC5B,CAAC;IACJ,CAAC;IACD,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE,CAAC;QACzD,OAAO;YACL,IAAI,EAAE,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;YAC3B,IAAI,EAAE,cAAc,CAAC,OAAO;SAC7B,CAAC;IACJ,CAAC;IACD,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE,CAAC;QAC/C,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,IAAA,4BAAe,EAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,OAAO;gBACL,IAAI,EAAE,IAAI,IAAI,GAAG;gBACjB,IAAI,EAAE,cAAc,CAAC,MAAM;aAC5B,CAAC;QACJ,CAAC;QACD,OAAO;YACL,IAAI;YACJ,IAAI,EAAE,cAAc,CAAC,MAAM;SAC5B,CAAC;IACJ,CAAC;IAED,OAAO;QACL,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;QAChD,IAAI,EAAE,cAAc,CAAC,UAAU;KAChC,CAAC;AACJ,CAAC;AAWD,SAAS,YAAY,CAAmB,MAA0B;IAChE,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAQ,CAAC;AAClE,CAAC;AAED;;;;;GAKG;AACH,SAAS,cAAc,CAAC,KAAe;IACrC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QAClB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACvB,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IAED,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC3E,CAAC;AAED;;;;;GAKG;AACH,SAAS,aAAa,CACpB,OAAY,EACZ,SAAoD;IAEpD,IAAI,GAAG,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;IAE7B,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC;QAChB,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;QACtC,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,GAAG,CAAC;QACb,CAAC;QACD,GAAG,EAAE,CAAC;IACR,CAAC;IAED,OAAO,CAAC,CAAC,CAAC;AACZ,CAAC;AAED,SAAS,2BAA2B,CAClC,IAAuB,EACvB,IAAY;IAEZ,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;QAC3C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;QAC9C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;QAC9C,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAClE,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAC1E,CAAC;AACJ,CAAC;AAED,SAAS,0BAA0B,CAAC,IAAoB;IACtD,OAAO,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC;AAC7D,CAAC;AAED,SAAS,wBAAwB,CAC/B,IAAsC,EACtC,UAA+B;IAE/B,OAAO,CACL,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAA,0BAAe,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CACzE,CAAC;AACJ,CAAC;AAUD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4CG;AACH,SAAS,0BAA0B,CACjC,IAAiB,EACjB,EAAE,UAAU,EAAkC;IAE9C,MAAM,GAAG,GACP,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;IAC3E,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC;IACrB,IACE,CAAC,IAAI,CAAC,QAAQ;QACd,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;YACjC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,CAAC,EAC5C,CAAC;QACD,OAAO,GAAG,CAAC,IAAI,CAAC;IAClB,CAAC;IACD,MAAM,MAAM,GAAG,IAAA,yBAAc,EAAC,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9D,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC;IACzB,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC3D,CAAC;AAED;;;;GAIG;AACH,MAAM,2BAA2B,GAAG,CAClC,gBAA6B,EAC7B,OAAuC,EACvC,GAAG,MAA2B,EACrB,EAAE,CACV,MAA0C,CAAC,QAAQ,CAClD,0BAA0B,CAAC,gBAAgB,EAAE,OAAO,CAAC,CACtD,CAAC;AAiBF,kEAA2B"}