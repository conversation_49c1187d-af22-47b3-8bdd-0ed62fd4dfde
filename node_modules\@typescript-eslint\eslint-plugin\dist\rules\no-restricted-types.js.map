{"version": 3, "file": "no-restricted-types.js", "sourceRoot": "", "sources": ["../../src/rules/no-restricted-types.ts"], "names": [], "mappings": ";;AAEA,oDAA0D;AAE1D,kCAAsD;AAuBtD,SAAS,YAAY,CAAC,GAAW;IAC/B,OAAO,GAAG,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACnC,CAAC;AAED,SAAS,aAAa,CACpB,IAAmB,EACnB,UAA+B;IAE/B,OAAO,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAChD,CAAC;AAED,SAAS,gBAAgB,CACvB,UAAyE;IAEzE,IAAI,CAAC,UAAU,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;QACvC,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;QACnC,OAAO,IAAI,UAAU,EAAE,CAAC;IAC1B,CAAC;IAED,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;QACvB,OAAO,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;IAClC,CAAC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,MAAM,aAAa,GAAG;IACpB,MAAM,EAAE,sBAAc,CAAC,eAAe;IACtC,OAAO,EAAE,sBAAc,CAAC,gBAAgB;IACxC,KAAK,EAAE,sBAAc,CAAC,cAAc;IACpC,IAAI,EAAE,sBAAc,CAAC,aAAa;IAClC,MAAM,EAAE,sBAAc,CAAC,eAAe;IACtC,MAAM,EAAE,sBAAc,CAAC,eAAe;IACtC,MAAM,EAAE,sBAAc,CAAC,eAAe;IACtC,MAAM,EAAE,sBAAc,CAAC,eAAe;IACtC,SAAS,EAAE,sBAAc,CAAC,kBAAkB;IAC5C,OAAO,EAAE,sBAAc,CAAC,gBAAgB;IACxC,IAAI,EAAE,sBAAc,CAAC,aAAa;CACnC,CAAC;AAEF,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,qBAAqB;IAC3B,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,wBAAwB;SACtC;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,iBAAiB,EAAE,kDAAkD;YACrE,qBAAqB,EAAE,4CAA4C;SACpE;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE;oBACL,SAAS,EAAE;wBACT,KAAK,EAAE;4BACL;gCACE,IAAI,EAAE,SAAS;gCACf,WAAW,EAAE,yCAAyC;gCACtD,IAAI,EAAE,CAAC,IAAI,CAAC;6BACb;4BACD;gCACE,IAAI,EAAE,QAAQ;gCACd,WAAW,EAAE,sCAAsC;6BACpD;4BACD;gCACE,IAAI,EAAE,QAAQ;gCACd,oBAAoB,EAAE,KAAK;gCAC3B,WAAW,EAAE,cAAc;gCAC3B,UAAU,EAAE;oCACV,OAAO,EAAE;wCACP,IAAI,EAAE,QAAQ;wCACd,WAAW,EACT,+HAA+H;qCAClI;oCACD,OAAO,EAAE;wCACP,IAAI,EAAE,QAAQ;wCACd,WAAW,EAAE,uBAAuB;qCACrC;oCACD,OAAO,EAAE;wCACP,IAAI,EAAE,OAAO;wCACb,WAAW,EAAE,kCAAkC;wCAC/C,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;qCAC1B;iCACF;6BACF;yBACF;qBACF;iBACF;gBACD,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,oBAAoB,EAAE;4BACpB,IAAI,EAAE,2BAA2B;yBAClC;wBACD,WAAW,EACT,wFAAwF;qBAC3F;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE,CAAC,EAAE,CAAC;IACpB,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,CAAC;QAC9B,MAAM,WAAW,GAAG,IAAI,GAAG,CACzB,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CACxE,CAAC;QAEF,SAAS,gBAAgB,CACvB,QAAuB,EACvB,IAAI,GAAG,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC;YAElD,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAEzC,IAAI,UAAU,KAAK,SAAS,IAAI,UAAU,KAAK,KAAK,EAAE,CAAC;gBACrD,OAAO;YACT,CAAC;YAED,MAAM,aAAa,GAAG,gBAAgB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,OAAO,GACX,UAAU,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,CAAC,OAAO,CAAC;YACrE,MAAM,OAAO,GACX,UAAU,IAAI,OAAO,UAAU,KAAK,QAAQ;gBAC1C,CAAC,CAAC,UAAU,CAAC,OAAO;gBACpB,CAAC,CAAC,SAAS,CAAC;YAEhB,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI,EAAE,QAAQ;gBACd,SAAS,EAAE,mBAAmB;gBAC9B,IAAI,EAAE;oBACJ,IAAI;oBACJ,aAAa;iBACd;gBACD,GAAG,EAAE,OAAO;oBACV,CAAC,CAAC,CAAC,KAAK,EAAoB,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC;oBACnE,CAAC,CAAC,IAAI;gBACR,OAAO,EAAE,OAAO,EAAE,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;oBACpC,SAAS,EAAE,uBAAuB;oBAClC,IAAI,EAAE;wBACJ,IAAI;wBACJ,WAAW;qBACZ;oBACD,GAAG,EAAE,CAAC,KAAK,EAAoB,EAAE,CAC/B,KAAK,CAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,CAAC;iBAC3C,CAAC,CAAC;aACJ,CAAC,CAAC;QACL,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAA,sBAAe,EACtC,aAAa,EACb,CAAC,GAA0B,EAAE,OAAO,EAAE,EAAE;YACtC,IAAI,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC7B,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAmB,EAAQ,EAAE,CAC1D,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACpC,CAAC;YAED,OAAO,GAAG,CAAC;QACb,CAAC,EACD,EAAE,CACH,CAAC;QAEF,OAAO;YACL,GAAG,gBAAgB;YAEnB,iBAAiB,CAAC,IAAI;gBACpB,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;YACD,mBAAmB,CAAC,IAAI;gBACtB,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;YACD,WAAW,CAAC,IAAI;gBACd,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;oBAC9B,gBAAgB,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;YACD,aAAa,CAAC,IAAI;gBAChB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;oBACzB,gBAAgB,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;YACD,eAAe,CAAC,IAAI;gBAClB,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAEhC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;oBACvB,gBAAgB,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}