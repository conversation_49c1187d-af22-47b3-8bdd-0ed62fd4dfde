{"version": 3, "file": "isNodeEqual.js", "sourceRoot": "", "sources": ["../../src/util/isNodeEqual.ts"], "names": [], "mappings": ";;AAIA,kCA4BC;AA9BD,oDAA0D;AAE1D,SAAgB,WAAW,CAAC,CAAgB,EAAE,CAAgB;IAC5D,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;QACtB,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IACE,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;QACxC,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EACxC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE,CAAC;QAC3E,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,CAAC;IAC7B,CAAC;IACD,IACE,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;QACpC,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EACpC,CAAC;QACD,OAAO,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;IAC3B,CAAC;IACD,IACE,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;QAC1C,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAC1C,CAAC;QACD,OAAO,CACL,WAAW,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CACvE,CAAC;IACJ,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC"}